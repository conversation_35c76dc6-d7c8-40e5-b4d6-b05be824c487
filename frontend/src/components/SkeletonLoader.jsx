import React from 'react';
import { 
  Box, 
  Skeleton, 
  Paper, 
  Typography,
  Card,
  CardContent 
} from '@mui/material';

// 测试用例骨架屏
export const TestCaseSkeleton = ({ count = 3 }) => {
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      {Array.from({ length: count }).map((_, index) => (
        <Paper 
          key={index}
          elevation={1} 
          sx={{ 
            p: 2, 
            borderRadius: 2,
            animation: 'pulse 1.5s ease-in-out infinite',
            '@keyframes pulse': {
              '0%': { opacity: 1 },
              '50%': { opacity: 0.7 },
              '100%': { opacity: 1 }
            }
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <Skeleton variant="text" width={80} height={32} />
            <Skeleton variant="text" width="60%" height={24} />
            <Skeleton variant="rectangular" width={60} height={24} sx={{ borderRadius: 1 }} />
          </Box>
          
          <Box sx={{ mb: 2 }}>
            <Skeleton variant="text" width={100} height={20} sx={{ mb: 1 }} />
            <Skeleton variant="text" width="80%" height={16} />
            <Skeleton variant="text" width="60%" height={16} />
          </Box>
          
          <Box>
            <Skeleton variant="text" width={120} height={20} sx={{ mb: 1 }} />
            <Skeleton variant="rectangular" width="100%" height={120} sx={{ borderRadius: 1 }} />
          </Box>
        </Paper>
      ))}
    </Box>
  );
};

// 流式输出骨架屏
export const StreamingSkeleton = () => {
  return (
    <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
      <Box sx={{ mb: 2 }}>
        <Skeleton variant="rectangular" width="100%" height={4} sx={{ borderRadius: 2 }} />
      </Box>
      
      <Box sx={{
        backgroundColor: '#f5f5f5',
        borderRadius: 1,
        p: 2,
        minHeight: '200px'
      }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
          <Skeleton variant="text" width="90%" height={20} />
          <Skeleton variant="text" width="75%" height={20} />
          <Skeleton variant="text" width="85%" height={20} />
          <Skeleton variant="text" width="60%" height={20} />
          <Box sx={{ mt: 2 }}>
            <Skeleton variant="text" width="95%" height={20} />
            <Skeleton variant="text" width="80%" height={20} />
            <Skeleton variant="text" width="70%" height={20} />
          </Box>
          <Box sx={{ mt: 2 }}>
            <Skeleton variant="rectangular" width="100%" height={60} sx={{ borderRadius: 1 }} />
          </Box>
        </Box>
      </Box>
    </Paper>
  );
};

// 工作区骨架屏
export const WorkspaceSkeleton = () => {
  return (
    <Paper elevation={3} sx={{ p: 4, borderRadius: 3 }}>
      <Box sx={{ mb: 3 }}>
        <Skeleton variant="text" width={200} height={32} sx={{ mb: 2 }} />
        <Skeleton variant="text" width="80%" height={20} />
      </Box>
      
      <Box sx={{ mb: 3 }}>
        <Skeleton variant="rectangular" width="100%" height={120} sx={{ borderRadius: 2, mb: 2 }} />
        <Skeleton variant="text" width={150} height={20} />
      </Box>
      
      <Box sx={{ mb: 3 }}>
        <Skeleton variant="text" width={120} height={24} sx={{ mb: 1 }} />
        <Skeleton variant="rectangular" width="100%" height={80} sx={{ borderRadius: 1, mb: 2 }} />
      </Box>
      
      <Box sx={{ mb: 3 }}>
        <Skeleton variant="text" width={100} height={24} sx={{ mb: 1 }} />
        <Skeleton variant="rectangular" width="100%" height={80} sx={{ borderRadius: 1, mb: 2 }} />
      </Box>
      
      <Box sx={{ display: 'flex', gap: 2 }}>
        <Skeleton variant="rectangular" width={120} height={40} sx={{ borderRadius: 3 }} />
        <Skeleton variant="rectangular" width={100} height={40} sx={{ borderRadius: 3 }} />
      </Box>
    </Paper>
  );
};

// 通用加载指示器
export const LoadingIndicator = ({ 
  message = "正在处理...", 
  progress = null,
  showProgress = false 
}) => {
  return (
    <Box sx={{ 
      display: 'flex', 
      flexDirection: 'column', 
      alignItems: 'center', 
      gap: 2,
      p: 4 
    }}>
      <Box sx={{ 
        width: 60, 
        height: 60, 
        borderRadius: '50%',
        background: 'linear-gradient(45deg, #1a73e8 30%, #4285f4 90%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        animation: 'spin 2s linear infinite',
        '@keyframes spin': {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(360deg)' }
        }
      }}>
        <Box sx={{
          width: 40,
          height: 40,
          borderRadius: '50%',
          backgroundColor: 'white'
        }} />
      </Box>
      
      <Typography variant="h6" color="primary" sx={{ fontWeight: 600 }}>
        {message}
      </Typography>
      
      {showProgress && progress !== null && (
        <Box sx={{ width: '100%', maxWidth: 300 }}>
          <Skeleton 
            variant="rectangular" 
            width={`${progress}%`} 
            height={4} 
            sx={{ borderRadius: 2 }} 
          />
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1, textAlign: 'center' }}>
            {Math.round(progress)}%
          </Typography>
        </Box>
      )}
    </Box>
  );
};

// 错误状态组件
export const ErrorState = ({ 
  title = "出现错误", 
  message = "请稍后重试", 
  onRetry = null 
}) => {
  return (
    <Card sx={{ 
      maxWidth: 400, 
      mx: 'auto', 
      mt: 4,
      border: '1px solid #ffcdd2',
      backgroundColor: '#ffebee'
    }}>
      <CardContent sx={{ textAlign: 'center', p: 4 }}>
        <Typography variant="h5" color="error" sx={{ mb: 2, fontWeight: 600 }}>
          ⚠️ {title}
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          {message}
        </Typography>
        {onRetry && (
          <Box sx={{ mt: 2 }}>
            <Skeleton variant="rectangular" width={100} height={36} sx={{ borderRadius: 2, mx: 'auto' }} />
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

// 空状态组件
export const EmptyState = ({ 
  title = "暂无数据", 
  message = "请上传文件开始生成测试用例",
  icon = "📄"
}) => {
  return (
    <Box sx={{ 
      textAlign: 'center', 
      py: 8,
      px: 4
    }}>
      <Typography variant="h1" sx={{ fontSize: '4rem', mb: 2 }}>
        {icon}
      </Typography>
      <Typography variant="h5" color="text.primary" sx={{ mb: 2, fontWeight: 600 }}>
        {title}
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ maxWidth: 400, mx: 'auto' }}>
        {message}
      </Typography>
    </Box>
  );
};

export default {
  TestCaseSkeleton,
  StreamingSkeleton,
  WorkspaceSkeleton,
  LoadingIndicator,
  ErrorState,
  EmptyState
};
