import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
  Stack,
  Alert,
  LinearProgress,
  IconButton,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText
} from '@mui/material';
import {
  CloudUpload,
  Description,
  Image,
  Code,
  AutoAwesome,
  PlayArrow,
  Refresh,
  Info
} from '@mui/icons-material';

// 使用React.memo优化组件
const WorkspaceArea = React.memo(({
  uploadedFile,
  onFileUpload,
  onGenerateTestCases,
  isGenerating,
  serverStatus,
  availableModels,
  onLoadModels
}) => {
  const [context, setContext] = useState('');
  const [requirements, setRequirements] = useState('');
  const [selectedModel, setSelectedModel] = useState('auto');

  // 组件加载时获取可用模型
  useEffect(() => {
    if (onLoadModels) {
      onLoadModels();
    }
  }, [onLoadModels]);

  // 使用useMemo缓存验证结果
  const validationState = useMemo(() => ({
    isContextValid: context.trim().length >= 10,
    isRequirementsValid: requirements.trim().length >= 10
  }), [context, requirements]);

  // 使用useCallback优化事件处理函数
  const handleFileChange = useCallback((event) => {
    const file = event.target.files[0];
    if (file) {
      onFileUpload(file);
    }
  }, [onFileUpload]);

  const handleSubmit = useCallback((e) => {
    e.preventDefault();
    onGenerateTestCases(context, requirements, selectedModel);
  }, [context, requirements, selectedModel, onGenerateTestCases]);

  const handleContextChange = useCallback((e) => {
    setContext(e.target.value);
  }, []);

  const handleRequirementsChange = useCallback((e) => {
    setRequirements(e.target.value);
  }, []);

  const handleModelChange = useCallback((e) => {
    setSelectedModel(e.target.value);
  }, []);

  // 使用useMemo缓存文件类型信息
  const getFileTypeInfo = useCallback((file) => {
    if (!file) return null;

    const extension = file.name.split('.').pop().toLowerCase();
    const fileTypes = {
      'png': { icon: <Image />, type: '图像文件', color: '#4caf50' },
      'jpg': { icon: <Image />, type: '图像文件', color: '#4caf50' },
      'jpeg': { icon: <Image />, type: '图像文件', color: '#4caf50' },
      'gif': { icon: <Image />, type: '图像文件', color: '#4caf50' },
      'pdf': { icon: <Description />, type: 'PDF文档', color: '#f44336' },
      'json': { icon: <Code />, type: 'API文档', color: '#ff9800' },
      'yaml': { icon: <Code />, type: 'API文档', color: '#ff9800' },
      'yml': { icon: <Code />, type: 'API文档', color: '#ff9800' }
    };

    return fileTypes[extension] || { icon: <Description />, type: '文档', color: '#9e9e9e' };
  }, []);

  const fileInfo = useMemo(() => getFileTypeInfo(uploadedFile), [uploadedFile, getFileTypeInfo]);

  return (
    <Paper 
      elevation={3} 
      sx={{ 
        p: 4, 
        borderRadius: 3,
        background: 'linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)',
        border: '1px solid rgba(0,0,0,0.05)'
      }}
    >
      {/* 标题区域 */}
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: 48,
            height: 48,
            borderRadius: 3,
            background: 'linear-gradient(135deg, #1a73e8 0%, #4285f4 100%)',
            boxShadow: '0 4px 12px rgba(26, 115, 232, 0.3)',
          }}>
            <AutoAwesome sx={{ color: '#fff', fontSize: 24 }} />
          </Box>
          <Box>
            <Typography
              variant="h5"
              component="h2"
              sx={{
                fontWeight: 700,
                background: 'linear-gradient(135deg, #1a73e8 0%, #8430ce 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                lineHeight: 1.2,
                mb: 0.5
              }}
            >
              智能工作台
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: '#5f6368',
                fontWeight: 500,
                letterSpacing: '0.5px'
              }}
            >
              上传文件，选择模型，生成测试用例
            </Typography>
          </Box>
        </Box>

        {/* AI模型选择 - 移到标题行 */}
        <Box sx={{ minWidth: 200, maxWidth: 300 }}>
          <FormControl size="small" variant="outlined" sx={{ minWidth: 200 }}>
            <InputLabel>AI模型</InputLabel>
            <Select
              value={selectedModel}
              onChange={handleModelChange}
              label="AI模型"
              sx={{
                borderRadius: 2,
                backgroundColor: 'rgba(255, 255, 255, 0.8)',
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'rgba(26, 115, 232, 0.3)'
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'rgba(26, 115, 232, 0.5)'
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#1a73e8'
                }
              }}
            >
              <MenuItem value="auto">
                <em>自动选择（推荐）</em>
              </MenuItem>
              {availableModels && availableModels.available_models && availableModels.available_models.map((model) => (
                <MenuItem key={model.id} value={model.id}>
                  <Box>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {model.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {model.description}
                    </Typography>
                  </Box>
                </MenuItem>
              ))}
            </Select>
            <FormHelperText sx={{ fontSize: '0.7rem' }}>
              {selectedModel && selectedModel !== 'auto' ? (
                availableModels && availableModels.available_models &&
                availableModels.available_models.find(m => m.id === selectedModel)?.recommended_for?.join('、')
              ) : (
                '系统将自动选择最适合的模型'
              )}
            </FormHelperText>
          </FormControl>
        </Box>
      </Box>

      <form onSubmit={handleSubmit}>
        <Grid container spacing={4}>
          {/* 文件上传区域 */}
          <Grid item xs={12}>
            <Card 
              elevation={0} 
              sx={{ 
                border: uploadedFile ? '2px solid #4caf50' : '2px dashed #1a73e8',
                borderRadius: 3,
                transition: 'all 0.3s ease',
                '&:hover': {
                  borderColor: uploadedFile ? '#4caf50' : '#0d47a1',
                  transform: 'translateY(-2px)',
                  boxShadow: '0 8px 25px rgba(0,0,0,0.1)'
                }
              }}
            >
              <CardContent sx={{ p: 4 }}>
                {uploadedFile ? (
                  // 已上传文件显示
                  <Box sx={{ textAlign: 'center' }}>
                    <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                      <Chip
                        icon={fileInfo?.icon}
                        label={fileInfo?.type}
                        sx={{
                          bgcolor: fileInfo?.color,
                          color: 'white',
                          fontWeight: 600,
                          '& .MuiChip-icon': { color: 'white' }
                        }}
                      />
                    </Box>
                    
                    {uploadedFile.type.startsWith('image/') ? (
                      <Box sx={{ mb: 2 }}>
                        <img
                          src={URL.createObjectURL(uploadedFile)}
                          alt="预览"
                          style={{
                            maxWidth: '200px',
                            maxHeight: '150px',
                            borderRadius: '8px',
                            boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
                          }}
                        />
                      </Box>
                    ) : (
                      <Box sx={{ mb: 2 }}>
                        {fileInfo?.icon && React.cloneElement(fileInfo.icon, {
                          sx: { fontSize: 64, color: fileInfo.color }
                        })}
                      </Box>
                    )}
                    
                    <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                      {uploadedFile.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      文件大小: {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
                    </Typography>
                    
                    <Button
                      variant="outlined"
                      component="label"
                      startIcon={<Refresh />}
                      sx={{ borderRadius: 2 }}
                    >
                      更换文件
                      <input
                        type="file"
                        hidden
                        accept="image/*,.pdf,.json,.yaml,.yml"
                        onChange={handleFileChange}
                      />
                    </Button>
                  </Box>
                ) : (
                  // 文件上传区域
                  <Box sx={{ textAlign: 'center' }}>
                    <CloudUpload sx={{ fontSize: 64, color: '#1a73e8', mb: 2 }} />
                    <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                      拖放文件到这里或点击上传
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                      支持图像文件、PDF需求文档、OpenAPI规范文档
                    </Typography>
                    
                    <Stack direction="row" spacing={1} justifyContent="center" sx={{ mb: 3 }}>
                      <Chip icon={<Image />} label="图像" size="small" variant="outlined" />
                      <Chip icon={<Description />} label="PDF" size="small" variant="outlined" />
                      <Chip icon={<Code />} label="API" size="small" variant="outlined" />
                    </Stack>
                    
                    <Button
                      variant="contained"
                      component="label"
                      startIcon={<CloudUpload />}
                      sx={{ borderRadius: 2, px: 4 }}
                    >
                      选择文件
                      <input
                        type="file"
                        hidden
                        accept="image/*,.pdf,.json,.yaml,.yml"
                        onChange={handleFileChange}
                      />
                    </Button>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* 上下文信息 */}
          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1 }}>
                测试上下文
                <Tooltip title="描述被测试的系统、功能或模块的基本信息">
                  <IconButton size="small" sx={{ ml: 1 }}>
                    <Info fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Typography>
            </Box>
            <TextField
              multiline
              rows={4}
              fullWidth
              value={context}
              onChange={handleContextChange}
              placeholder="例如：这是一个电商网站的用户登录功能，包含用户名密码登录、手机号登录、第三方登录等方式..."
              variant="outlined"
              required
              error={context.length > 0 && !validationState.isContextValid}
              helperText={
                context.length > 0 && !validationState.isContextValid
                  ? `请至少输入10个字符 (当前: ${context.trim().length}/10)`
                  : context.length > 0
                    ? `已输入 ${context.trim().length} 个字符`
                    : ''
              }
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2
                }
              }}
            />
          </Grid>

          {/* 需求描述 */}
          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1 }}>
                测试需求
                <Tooltip title="描述希望生成的测试用例类型和重点关注的测试场景">
                  <IconButton size="small" sx={{ ml: 1 }}>
                    <Info fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Typography>
            </Box>
            <TextField
              multiline
              rows={4}
              fullWidth
              value={requirements}
              onChange={handleRequirementsChange}
              placeholder="例如：需要生成包含正向测试、异常测试、边界测试的完整测试用例，重点关注安全性和用户体验..."
              variant="outlined"
              required
              error={requirements.length > 0 && !validationState.isRequirementsValid}
              helperText={
                requirements.length > 0 && !validationState.isRequirementsValid
                  ? `请至少输入10个字符 (当前: ${requirements.trim().length}/10)`
                  : requirements.length > 0
                    ? `已输入 ${requirements.trim().length} 个字符`
                    : ''
              }
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2
                }
              }}
            />
          </Grid>



          {/* 状态提示 */}
          <Grid item xs={12}>
            {serverStatus === 'error' && (
              <Alert severity="error" sx={{ mb: 2, borderRadius: 2 }}>
                <Typography variant="body2">
                  无法连接到后端服务器，请确保服务器正在运行
                </Typography>
              </Alert>
            )}
            
            {isGenerating && (
              <Alert severity="info" sx={{ mb: 2, borderRadius: 2 }}>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  AI正在分析您的文件并生成测试用例，请稍候...
                </Typography>
                <LinearProgress sx={{ borderRadius: 1 }} />
              </Alert>
            )}
          </Grid>

          {/* 生成按钮 */}
          <Grid item xs={12}>
            <Box sx={{ textAlign: 'center' }}>
              <Button
                type="submit"
                variant="contained"
                size="large"
                disabled={!uploadedFile || isGenerating || !validationState.isContextValid || !validationState.isRequirementsValid || serverStatus !== 'connected'}
                startIcon={isGenerating ? <AutoAwesome className="rotating" /> : <PlayArrow />}
                sx={{
                  px: 6,
                  py: 2,
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  borderRadius: 3,
                  background: 'linear-gradient(45deg, #1a73e8 30%, #4285f4 90%)',
                  '&:hover': {
                    background: 'linear-gradient(45deg, #1557b0 30%, #3367d6 90%)',
                    transform: 'translateY(-2px)',
                    boxShadow: '0 8px 25px rgba(26, 115, 232, 0.3)'
                  },
                  '&:disabled': {
                    background: '#e0e0e0'
                  },
                  transition: 'all 0.3s ease'
                }}
              >
                {isGenerating ? '生成中...' : '🚀 开始生成测试用例'}
              </Button>
              
              {(!uploadedFile || !validationState.isContextValid || !validationState.isRequirementsValid) && (
                <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                  {!uploadedFile
                    ? '请先上传文件并填写相关信息'
                    : !validationState.isContextValid || !validationState.isRequirementsValid
                      ? '请确保测试上下文和测试需求都至少输入10个字符'
                      : '请填写完整信息'
                  }
                </Typography>
              )}
            </Box>
          </Grid>
        </Grid>
      </form>

      {/* 添加旋转动画 */}
      <style jsx>{`
        @keyframes rotate {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
        .rotating {
          animation: rotate 2s linear infinite;
        }
      `}</style>
    </Paper>
  );
});

// 设置displayName用于调试
WorkspaceArea.displayName = 'WorkspaceArea';

export default WorkspaceArea;
