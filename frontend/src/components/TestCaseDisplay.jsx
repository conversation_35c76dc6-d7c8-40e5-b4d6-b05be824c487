import React, { useState, useMemo, useCallback } from 'react';
import { FixedSizeList as List } from 'react-window';
import AutoSizer from 'react-virtualized-auto-sizer';
import Paper from '@mui/material/Paper';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Chip from '@mui/material/Chip';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import ToggleButton from '@mui/material/ToggleButton';
import ToggleButtonGroup from '@mui/material/ToggleButtonGroup';
import ViewListIcon from '@mui/icons-material/ViewList';
import ViewModuleIcon from '@mui/icons-material/ViewModule';

// 优化的测试用例项组件，使用React.memo避免不必要的重渲染
const TestCaseItem = React.memo(({ testCase, index, style }) => {
  const getPriorityColor = useCallback((priority) => {
    switch (priority?.toLowerCase()) {
      case 'high':
      case '高':
        return 'error';
      case 'medium':
      case '中':
        return 'warning';
      case 'low':
      case '低':
        return 'success';
      default:
        return 'default';
    }
  }, []);

  return (
    <div style={style}>
      <Paper
        elevation={1}
        sx={{
          m: 1,
          p: 2,
          borderRadius: 2,
          transition: 'all 0.2s ease',
          '&:hover': {
            elevation: 3,
            transform: 'translateY(-1px)'
          }
        }}
      >
        <Accordion defaultExpanded={index < 3}>
          <AccordionSummary
            expandIcon={<ExpandMoreIcon />}
            aria-controls={`panel${index}-content`}
            id={`panel${index}-header`}
            sx={{
              '& .MuiAccordionSummary-content': {
                alignItems: 'center',
                gap: 2
              }
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
              <Typography variant="h6" sx={{ fontWeight: 600, color: '#1a73e8' }}>
                {testCase.id || `TC-${index + 1}`}
              </Typography>
              <Typography variant="subtitle1" sx={{ flexGrow: 1 }}>
                {testCase.title}
              </Typography>
              {testCase.priority && (
                <Chip
                  label={testCase.priority}
                  color={getPriorityColor(testCase.priority)}
                  size="small"
                  variant="outlined"
                />
              )}
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {testCase.description && (
                <Box>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                    描述:
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {testCase.description}
                  </Typography>
                </Box>
              )}

              {testCase.preconditions && (
                <Box>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                    前置条件:
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {testCase.preconditions}
                  </Typography>
                </Box>
              )}

              {testCase.steps && testCase.steps.length > 0 && (
                <Box>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                    测试步骤:
                  </Typography>
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell sx={{ fontWeight: 600 }}>#</TableCell>
                          <TableCell sx={{ fontWeight: 600 }}>步骤描述</TableCell>
                          <TableCell sx={{ fontWeight: 600 }}>预期结果</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {testCase.steps.map((step, stepIndex) => (
                          <TableRow key={stepIndex}>
                            <TableCell>{step.step_number}</TableCell>
                            <TableCell>{step.description}</TableCell>
                            <TableCell>{step.expected_result}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Box>
              )}
            </Box>
          </AccordionDetails>
        </Accordion>
      </Paper>
    </div>
  );
});

const TestCaseDisplay = ({ testCases = [], onExportToExcel }) => {
  const [viewMode, setViewMode] = useState('list'); // 'list' or 'markdown'

  // 使用useMemo缓存计算结果，避免重复计算
  const markdownContent = useMemo(() => {
    if (!testCases || testCases.length === 0) return '';

    let markdown = '# 生成的测试用例\n\n';

    testCases.forEach((testCase, index) => {
      markdown += `## ${testCase.id || `TC-${index + 1}`}: ${testCase.title}\n\n`;

      if (testCase.priority) {
        markdown += `**优先级:** ${testCase.priority}\n\n`;
      }

      markdown += `**描述:** ${testCase.description}\n\n`;

      if (testCase.preconditions) {
        markdown += `**前置条件:** ${testCase.preconditions}\n\n`;
      }

      markdown += `### 测试步骤\n\n`;
      markdown += `| # | 步骤描述 | 预期结果 |\n`;
      markdown += `| --- | --- | --- |\n`;

      testCase.steps.forEach(step => {
        markdown += `| ${step.step_number} | ${step.description} | ${step.expected_result} |\n`;
      });

      markdown += '\n\n';
    });

    return markdown;
  }, [testCases]);

  // 虚拟列表渲染项
  const renderItem = useCallback(({ index, style }) => (
    <TestCaseItem
      testCase={testCases[index]}
      index={index}
      style={style}
    />
  ), [testCases]);

  const handleViewModeChange = useCallback((_, newMode) => {
    if (newMode !== null) {
      setViewMode(newMode);
    }
  }, []);

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="body1" color="text.secondary">
          已生成 {testCases.length} 个测试用例
        </Typography>

        <ToggleButtonGroup
          value={viewMode}
          exclusive
          onChange={handleViewModeChange}
          size="small"
          sx={{ borderRadius: 2 }}
        >
          <ToggleButton value="list" aria-label="列表视图">
            <ViewListIcon />
          </ToggleButton>
          <ToggleButton value="markdown" aria-label="Markdown视图">
            <ViewModuleIcon />
          </ToggleButton>
        </ToggleButtonGroup>
      </Box>

      {viewMode === 'list' ? (
        // 虚拟滚动列表视图
        <Box sx={{ flexGrow: 1, height: '600px', border: '1px solid #e0e0e0', borderRadius: 1 }}>
          <AutoSizer>
            {({ height, width }) => (
              <List
                height={height}
                width={width}
                itemCount={testCases.length}
                itemSize={200} // 每个项目的高度
                overscanCount={5} // 预渲染的项目数量
              >
                {renderItem}
              </List>
            )}
          </AutoSizer>
        </Box>
      ) : (
        // Markdown视图
        <Box sx={{
          flexGrow: 1,
          overflow: 'auto',
          bgcolor: '#f8f9fa',
          p: 3,
          borderRadius: 1,
          maxHeight: '600px'
        }}>
          <ReactMarkdown remarkPlugins={[remarkGfm]}>
            {markdownContent}
          </ReactMarkdown>
        </Box>
      )}
    </Box>
  );
};

export default TestCaseDisplay;
