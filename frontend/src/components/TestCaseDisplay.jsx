import React, { useState, useMemo, useCallback, useRef, useEffect } from 'react';
import { VariableSizeList as List } from 'react-window';
import AutoSizer from 'react-virtualized-auto-sizer';
import Paper from '@mui/material/Paper';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Chip from '@mui/material/Chip';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import ToggleButton from '@mui/material/ToggleButton';
import ToggleButtonGroup from '@mui/material/ToggleButtonGroup';
import ViewListIcon from '@mui/icons-material/ViewList';
import ViewModuleIcon from '@mui/icons-material/ViewModule';

// 优化的测试用例项组件，支持动态高度
const TestCaseItem = React.memo(({ testCase, index, style, onHeightChange }) => {
  const itemRef = useRef(null);
  const [isExpanded, setIsExpanded] = useState(index < 3); // 默认展开前3个
  const resizeObserverRef = useRef(null);

  const getPriorityColor = useCallback((priority) => {
    switch (priority?.toLowerCase()) {
      case 'high':
      case '高':
        return 'error';
      case 'medium':
      case '中':
        return 'warning';
      case 'low':
      case '低':
        return 'success';
      default:
        return 'default';
    }
  }, []);

  // 测量并更新高度的函数
  const measureAndUpdateHeight = useCallback(() => {
    if (itemRef.current && onHeightChange) {
      const height = itemRef.current.offsetHeight;
      onHeightChange(index, height);
    }
  }, [index, onHeightChange]);

  // 当展开状态改变时，通知父组件更新高度
  const handleAccordionChange = useCallback((_, expanded) => {
    setIsExpanded(expanded);
    // 使用多重延迟确保DOM完全更新
    requestAnimationFrame(() => {
      requestAnimationFrame(() => {
        setTimeout(() => {
          measureAndUpdateHeight();
        }, 50);
      });
    });
  }, [measureAndUpdateHeight]);

  // 使用ResizeObserver监听高度变化
  useEffect(() => {
    if (itemRef.current) {
      resizeObserverRef.current = new ResizeObserver(() => {
        measureAndUpdateHeight();
      });
      resizeObserverRef.current.observe(itemRef.current);

      // 初始测量
      measureAndUpdateHeight();
    }

    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
    };
  }, [measureAndUpdateHeight]);

  return (
    <div style={style}>
      <div ref={itemRef}>
        <Paper
          elevation={1}
          sx={{
            m: 1,
            borderRadius: 2,
            transition: 'all 0.2s ease',
            '&:hover': {
              elevation: 3,
              transform: 'translateY(-1px)'
            }
          }}
        >
          <Accordion
            expanded={isExpanded}
            onChange={handleAccordionChange}
            sx={{
              '&:before': {
                display: 'none',
              },
              boxShadow: 'none',
              borderRadius: 2,
            }}
          >
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              aria-controls={`panel${index}-content`}
              id={`panel${index}-header`}
              sx={{
                '& .MuiAccordionSummary-content': {
                  alignItems: 'center',
                  gap: 2
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                <Typography variant="h6" sx={{ fontWeight: 600, color: '#1a73e8' }}>
                  {testCase.id || `TC-${index + 1}`}
                </Typography>
                <Typography variant="subtitle1" sx={{ flexGrow: 1 }}>
                  {testCase.title}
                </Typography>
                {testCase.priority && (
                  <Chip
                    label={testCase.priority}
                    color={getPriorityColor(testCase.priority)}
                    size="small"
                    variant="outlined"
                  />
                )}
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {testCase.description && (
                  <Box>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                      描述:
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {testCase.description}
                    </Typography>
                  </Box>
                )}

                {testCase.preconditions && (
                  <Box>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                      前置条件:
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {testCase.preconditions}
                    </Typography>
                  </Box>
                )}

                {testCase.steps && testCase.steps.length > 0 && (
                  <Box>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                      测试步骤:
                    </Typography>
                    <TableContainer component={Paper} variant="outlined">
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell sx={{ fontWeight: 600 }}>#</TableCell>
                            <TableCell sx={{ fontWeight: 600 }}>步骤描述</TableCell>
                            <TableCell sx={{ fontWeight: 600 }}>预期结果</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {testCase.steps.map((step, stepIndex) => (
                            <TableRow key={stepIndex}>
                              <TableCell>{step.step_number}</TableCell>
                              <TableCell>{step.description}</TableCell>
                              <TableCell>{step.expected_result}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Box>
                )}
              </Box>
            </AccordionDetails>
          </Accordion>
        </Paper>
      </div>
    </div>
  );
});

const TestCaseDisplay = ({ testCases = [] }) => {
  const [viewMode, setViewMode] = useState('list'); // 'list' or 'markdown'
  const listRef = useRef(null);
  const itemHeights = useRef(new Map());
  const [, forceUpdate] = useState({});

  // 如果测试用例数量较少，直接渲染而不使用虚拟滚动
  const shouldUseVirtualScrolling = testCases.length > 20;

  // 强制重新渲染的函数
  const triggerRerender = useCallback(() => {
    forceUpdate({});
  }, []);

  // 使用useMemo缓存计算结果，避免重复计算
  const markdownContent = useMemo(() => {
    if (!testCases || testCases.length === 0) return '';

    let markdown = '# 生成的测试用例\n\n';

    testCases.forEach((testCase, index) => {
      markdown += `## ${testCase.id || `TC-${index + 1}`}: ${testCase.title}\n\n`;

      if (testCase.priority) {
        markdown += `**优先级:** ${testCase.priority}\n\n`;
      }

      markdown += `**描述:** ${testCase.description}\n\n`;

      if (testCase.preconditions) {
        markdown += `**前置条件:** ${testCase.preconditions}\n\n`;
      }

      markdown += `### 测试步骤\n\n`;
      markdown += `| # | 步骤描述 | 预期结果 |\n`;
      markdown += `| --- | --- | --- |\n`;

      testCase.steps.forEach(step => {
        markdown += `| ${step.step_number} | ${step.description} | ${step.expected_result} |\n`;
      });

      markdown += '\n\n';
    });

    return markdown;
  }, [testCases]);

  // 处理项目高度变化
  const handleHeightChange = useCallback((index, height) => {
    const currentHeight = itemHeights.current.get(index);
    if (currentHeight !== height) {
      itemHeights.current.set(index, height);
      if (listRef.current) {
        // 重置从当前索引开始的所有项目
        listRef.current.resetAfterIndex(index, false);
        // 触发重新渲染
        triggerRerender();
      }
    }
  }, [triggerRerender]);

  // 获取项目高度
  const getItemSize = useCallback((index) => {
    const height = itemHeights.current.get(index);
    if (height) {
      return height;
    }
    // 根据是否展开返回不同的估算高度
    return index < 3 ? 400 : 120; // 前3个默认展开，给更大的估算高度
  }, []);

  // 当测试用例数据变化时，清空高度缓存
  useEffect(() => {
    itemHeights.current.clear();
    if (listRef.current) {
      listRef.current.resetAfterIndex(0);
    }
  }, [testCases]);

  // 虚拟列表渲染项
  const renderItem = useCallback(({ index, style }) => (
    <TestCaseItem
      testCase={testCases[index]}
      index={index}
      style={style}
      onHeightChange={handleHeightChange}
    />
  ), [testCases, handleHeightChange]);

  // 非虚拟化渲染项（用于少量数据）
  const renderDirectItem = useCallback((testCase, index) => (
    <TestCaseItem
      key={testCase.id || index}
      testCase={testCase}
      index={index}
      style={{}} // 不需要虚拟滚动的样式
      onHeightChange={() => {}} // 不需要高度管理
    />
  ), []);

  const handleViewModeChange = useCallback((_, newMode) => {
    if (newMode !== null) {
      setViewMode(newMode);
    }
  }, []);

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="body1" color="text.secondary">
          已生成 {testCases.length} 个测试用例
        </Typography>

        <ToggleButtonGroup
          value={viewMode}
          exclusive
          onChange={handleViewModeChange}
          size="small"
          sx={{ borderRadius: 2 }}
        >
          <ToggleButton value="list" aria-label="列表视图">
            <ViewListIcon />
          </ToggleButton>
          <ToggleButton value="markdown" aria-label="Markdown视图">
            <ViewModuleIcon />
          </ToggleButton>
        </ToggleButtonGroup>
      </Box>

      {viewMode === 'list' ? (
        shouldUseVirtualScrolling ? (
          // 虚拟滚动列表视图 - 用于大量数据
          <Box sx={{ flexGrow: 1, height: '600px', border: '1px solid #e0e0e0', borderRadius: 1 }}>
            <AutoSizer>
              {({ height, width }) => (
                <List
                  ref={listRef}
                  height={height}
                  width={width}
                  itemCount={testCases.length}
                  itemSize={getItemSize}
                  overscanCount={2}
                  estimatedItemSize={250}
                  useIsScrolling={false}
                >
                  {renderItem}
                </List>
              )}
            </AutoSizer>
          </Box>
        ) : (
          // 直接渲染 - 用于少量数据，避免虚拟滚动的复杂性
          <Box sx={{
            flexGrow: 1,
            maxHeight: '600px',
            overflow: 'auto',
            border: '1px solid #e0e0e0',
            borderRadius: 1
          }}>
            {testCases.map((testCase, index) => renderDirectItem(testCase, index))}
          </Box>
        )
      ) : (
        // Markdown视图
        <Box sx={{
          flexGrow: 1,
          overflow: 'auto',
          bgcolor: '#f8f9fa',
          p: 3,
          borderRadius: 1,
          maxHeight: '600px'
        }}>
          <ReactMarkdown remarkPlugins={[remarkGfm]}>
            {markdownContent}
          </ReactMarkdown>
        </Box>
      )}
    </Box>
  );
};

export default TestCaseDisplay;
