import React, { useEffect, useRef, useMemo } from 'react';
import Paper from '@mui/material/Paper';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import LinearProgress from '@mui/material/LinearProgress';
import Chip from '@mui/material/Chip';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { StreamingSkeleton } from './SkeletonLoader';

const StreamingOutput = ({ content, isGenerating = true }) => {
  const outputRef = useRef(null);

  // 使用useMemo优化内容处理
  const processedContent = useMemo(() => {
    if (!content) return '';

    // 计算生成进度（基于内容长度的简单估算）
    const estimatedProgress = Math.min((content.length / 5000) * 100, 95);

    return {
      text: content,
      progress: estimatedProgress,
      wordCount: content.split(/\s+/).length,
      lineCount: content.split('\n').length
    };
  }, [content]);

  // 当内容更新时自动滚动到底部
  useEffect(() => {
    if (outputRef.current) {
      outputRef.current.scrollTop = outputRef.current.scrollHeight;
    }
  }, [content]);

  // 如果没有内容且正在生成，显示骨架屏
  if (!content && isGenerating) {
    return <StreamingSkeleton />;
  }

  return (
    <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
      {/* 进度指示器和统计信息 */}
      <Box sx={{ mb: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography variant="body2" color="text.secondary">
            {isGenerating ? '🤖 AI正在生成内容...' : '✅ 生成完成'}
          </Typography>

          {processedContent.text && (
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Chip
                label={`${processedContent.wordCount} 词`}
                size="small"
                variant="outlined"
                color="primary"
              />
              <Chip
                label={`${processedContent.lineCount} 行`}
                size="small"
                variant="outlined"
                color="secondary"
              />
            </Box>
          )}
        </Box>

        {isGenerating && (
          <LinearProgress
            variant={processedContent.progress > 0 ? "determinate" : "indeterminate"}
            value={processedContent.progress}
            sx={{
              height: 6,
              borderRadius: 3,
              backgroundColor: '#e3f2fd',
              '& .MuiLinearProgress-bar': {
                borderRadius: 3,
                background: 'linear-gradient(45deg, #1a73e8 30%, #4285f4 90%)'
              }
            }}
          />
        )}
      </Box>

      {/* 内容显示区域 */}
      <Box
        ref={outputRef}
        sx={{
          backgroundColor: '#f5f5f5',
          borderRadius: 1,
          p: 2,
          maxHeight: '400px',
          overflowY: 'auto',
          wordBreak: 'break-word',
          position: 'relative',
          border: '1px solid #e0e0e0',
          transition: 'all 0.3s ease',
          '&:hover': {
            borderColor: '#1a73e8'
          }
        }}
      >
        {processedContent.text ? (
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            components={{
              // 自定义组件样式
              h1: ({ children }) => (
                <Typography variant="h4" sx={{ mb: 2, color: '#1a73e8', fontWeight: 600 }}>
                  {children}
                </Typography>
              ),
              h2: ({ children }) => (
                <Typography variant="h5" sx={{ mb: 1.5, mt: 2, color: '#1a73e8', fontWeight: 600 }}>
                  {children}
                </Typography>
              ),
              h3: ({ children }) => (
                <Typography variant="h6" sx={{ mb: 1, mt: 1.5, color: '#1a73e8', fontWeight: 600 }}>
                  {children}
                </Typography>
              ),
              p: ({ children }) => (
                <Typography variant="body1" sx={{ mb: 1, lineHeight: 1.6 }}>
                  {children}
                </Typography>
              ),
              strong: ({ children }) => (
                <Typography component="span" sx={{ fontWeight: 600, color: '#1a73e8' }}>
                  {children}
                </Typography>
              )
            }}
          >
            {processedContent.text}
          </ReactMarkdown>
        ) : (
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: 100,
            color: 'text.secondary'
          }}>
            <Typography variant="body2">
              {isGenerating ? '⏳ 等待AI响应...' : '📝 暂无内容'}
            </Typography>
          </Box>
        )}

        {/* 实时打字效果指示器 */}
        {isGenerating && processedContent.text && (
          <Box
            sx={{
              display: 'inline-block',
              width: 2,
              height: 20,
              backgroundColor: '#1a73e8',
              animation: 'blink 1s infinite',
              '@keyframes blink': {
                '0%, 50%': { opacity: 1 },
                '51%, 100%': { opacity: 0 }
              }
            }}
          />
        )}
      </Box>
    </Paper>
  );
};

export default StreamingOutput;
